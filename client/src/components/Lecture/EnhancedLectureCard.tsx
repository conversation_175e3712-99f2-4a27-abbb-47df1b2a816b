import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import NextVideoPlayer from './NextVideoPlayer';
import { 
  Video, 
  FileText, 
  Clock, 
  Eye, 
  Edit, 
  Play,
  Download,
  MoreHorizontal,
  CheckCircle,
  Lock
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/utils/formatDuration';

interface Lecture {
  _id: string;
  lectureTitle: string;
  description?: string;
  videoUrl?: string;
  pdfUrl?: string;
  thumbnailUrl?: string;
  duration?: number;
  order: number;
  isPreviewFree?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface EnhancedLectureCardProps {
  lecture: Lecture;
  index: number;
  isCompleted?: boolean;
  isLocked?: boolean;
  showActions?: boolean;
  variant?: 'student' | 'teacher';
  onPlay?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onPreview?: () => void;
  onDownload?: () => void;
  className?: string;
}

const EnhancedLectureCard: React.FC<EnhancedLectureCardProps> = ({
  lecture,
  index,
  isCompleted = false,
  isLocked = false,
  showActions = true,
  variant = 'teacher',
  onPlay,
  onEdit,
  onDelete,
  onPreview,
  onDownload,
  className
}) => {
  const getContentType = () => {
    if (lecture.videoUrl && lecture.pdfUrl) return 'mixed';
    if (lecture.videoUrl) return 'video';
    if (lecture.pdfUrl) return 'pdf';
    return 'empty';
  };

  const contentType = getContentType();

  const getStatusIcon = () => {
    if (isCompleted) return <CheckCircle className="w-4 h-4 text-green-600" />;
    if (isLocked) return <Lock className="w-4 h-4 text-gray-400" />;
    if (lecture.isPreviewFree) return <Play className="w-4 h-4 text-blue-600" />;
    return <span className="text-sm font-medium text-gray-600">{index + 1}</span>;
  };

  const getCardStyles = () => {
    if (isCompleted) return "border-green-200 bg-green-50/50";
    if (isLocked) return "border-gray-200 bg-gray-50/50";
    if (lecture.isPreviewFree) return "border-blue-200 bg-blue-50/50";
    return "border-gray-200 hover:border-blue-300 hover:shadow-md";
  };

  return (
    <Card
      className={cn(
        "group transition-all duration-300 cursor-pointer overflow-hidden border-2 hover:border-blue-300 hover:shadow-md",
        getCardStyles(),
        isLocked && "opacity-75",
        className
      )}

      onClick={!isLocked ? onPlay : undefined}
    >
      {/* Simplified Header Section - No Video Display */}
      <div className="relative h-20 bg-gradient-to-r from-blue-500 to-purple-600 overflow-hidden">
        <div className="flex items-center justify-between h-full px-4">
          {/* Left side - Status and Order */}
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center">
              {getStatusIcon()}
            </div>
            <div className="text-white">
              <div className="text-sm font-medium">Lecture {index + 1}</div>
              {lecture.duration && (
                <div className="text-xs text-white/80 flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {formatDuration(lecture.duration)}
                </div>
              )}
            </div>
          </div>

          {/* Right side - Content type and Preview badge */}
          <div className="flex items-center gap-2">
            {lecture.isPreviewFree && (
              <Badge className="bg-white/20 text-white border-white/30">
                Preview
              </Badge>
            )}
            <div className="flex gap-1">
              {lecture.videoUrl && (
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <Video className="w-4 h-4 text-white" />
                </div>
              )}
              {lecture.pdfUrl && (
                <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                  <FileText className="w-4 h-4 text-white" />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Compact Content Section */}
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Title and description */}
          <div>
            <h3 className="font-semibold text-gray-900 line-clamp-1 mb-1 group-hover:text-blue-600 transition-colors">
              {lecture.lectureTitle}
            </h3>
            {lecture.description && (
              <p className="text-sm text-gray-600 line-clamp-1">
                {lecture.description}
              </p>
            )}
          </div>

          {/* Lecture Stats - Table Style */}
          <div className="grid grid-cols-3 gap-2 text-center py-2 border-t border-gray-100">
            <div>
              <div className="text-xs text-gray-500">Order</div>
              <div className="text-sm font-medium">{index + 1}</div>
            </div>
            <div>
              <div className="text-xs text-gray-500">Type</div>
              <div className="text-sm font-medium">
                {contentType === 'mixed' ? 'Video+PDF' :
                 contentType === 'video' ? 'Video' :
                 contentType === 'pdf' ? 'PDF' : 'Empty'}
              </div>
            </div>
            <div>
              <div className="text-xs text-gray-500">Status</div>
              <div className="text-sm font-medium">
                {isCompleted ? 'Complete' :
                 isLocked ? 'Locked' :
                 lecture.isPreviewFree ? 'Preview' : 'Available'}
              </div>
            </div>
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex gap-2 pt-2">
              <Button
                size="sm"
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                onClick={(e) => {
                  e.stopPropagation();
                  onPlay?.();
                }}
                disabled={isLocked}
              >
                <Play className="w-3 h-3 mr-1" />
                {variant === 'student' ? 'Watch' : 'Preview'}
              </Button>

              {variant === 'teacher' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit?.();
                  }}
                >
                  <Edit className="w-3 h-3 mr-1" />
                  Edit
                </Button>
              )}

              {/* More actions dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={onPreview}>
                    <Eye className="w-4 h-4 mr-2" />
                    Preview
                  </DropdownMenuItem>
                  {onDownload && (
                    <DropdownMenuItem onClick={onDownload}>
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </DropdownMenuItem>
                  )}
                  {variant === 'teacher' && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={onEdit}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Lecture
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={onDelete}
                        className="text-red-600 focus:text-red-600"
                      >
                        <FileText className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedLectureCard;
