import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import NextVideoPlayer from './NextVideoPlayer';
import { 
  Video, 
  FileText, 
  Clock, 
  Eye, 
  Edit, 
  Play,
  Download,
  MoreHorizontal,
  CheckCircle,
  Lock
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/utils/formatDuration';

interface Lecture {
  _id: string;
  lectureTitle: string;
  description?: string;
  videoUrl?: string;
  pdfUrl?: string;
  thumbnailUrl?: string;
  duration?: number;
  order: number;
  isPreviewFree?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface EnhancedLectureCardProps {
  lecture: Lecture;
  index: number;
  isCompleted?: boolean;
  isLocked?: boolean;
  showActions?: boolean;
  variant?: 'student' | 'teacher';
  onPlay?: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  onPreview?: () => void;
  onDownload?: () => void;
  className?: string;
}

const EnhancedLectureCard: React.FC<EnhancedLectureCardProps> = ({
  lecture,
  index,
  isCompleted = false,
  isLocked = false,
  showActions = true,
  variant = 'teacher',
  onPlay,
  onEdit,
  onDelete,
  onPreview,
  onDownload,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getContentType = () => {
    if (lecture.videoUrl && lecture.pdfUrl) return 'mixed';
    if (lecture.videoUrl) return 'video';
    if (lecture.pdfUrl) return 'pdf';
    return 'empty';
  };

  const contentType = getContentType();

  const getStatusIcon = () => {
    if (isCompleted) return <CheckCircle className="w-4 h-4 text-green-600" />;
    if (isLocked) return <Lock className="w-4 h-4 text-gray-400" />;
    if (lecture.isPreviewFree) return <Play className="w-4 h-4 text-blue-600" />;
    return <span className="text-sm font-medium text-gray-600">{index + 1}</span>;
  };

  const getCardStyles = () => {
    if (isCompleted) return "border-green-200 bg-green-50/50";
    if (isLocked) return "border-gray-200 bg-gray-50/50";
    if (lecture.isPreviewFree) return "border-blue-200 bg-blue-50/50";
    return "border-gray-200 hover:border-blue-300 hover:shadow-md";
  };

  const getThumbnail = () => {
    if (lecture.thumbnailUrl) return lecture.thumbnailUrl;
    if (contentType === 'video') return '/images/default-video-thumbnail.jpg';
    if (contentType === 'pdf') return '/images/default-pdf-thumbnail.jpg';
    return '/images/default-lecture-thumbnail.jpg';
  };

  return (
    <Card 
      className={cn(
        "group transition-all duration-300 cursor-pointer overflow-hidden",
        getCardStyles(),
        isLocked && "opacity-75",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={!isLocked ? onPlay : undefined}
    >
      {/* Thumbnail/Header Section */}
      <div className="relative aspect-video bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
        {lecture.thumbnailUrl ? (
          <img 
            src={getThumbnail()} 
            alt={lecture.lectureTitle}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center mb-3 mx-auto">
                {contentType === 'video' && <Video className="w-8 h-8 text-white" />}
                {contentType === 'pdf' && <FileText className="w-8 h-8 text-white" />}
                {contentType === 'mixed' && (
                  <div className="flex gap-1">
                    <Video className="w-6 h-6 text-white" />
                    <FileText className="w-6 h-6 text-white" />
                  </div>
                )}
                {contentType === 'empty' && <div className="w-8 h-8 bg-white/30 rounded-full" />}
              </div>
              <div className="text-white/90 text-sm font-medium">
                Lecture {index + 1}
              </div>
            </div>
          </div>
        )}

        {/* Overlay with play button */}
        {isHovered && !isLocked && (
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center transition-all duration-300">
            <Button
              size="lg"
              className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm"
              variant="outline"
              onClick={(e) => {
                e.stopPropagation();
                onPlay?.();
              }}
            >
              <Play className="w-6 h-6 mr-2" />
              {variant === 'student' ? 'Watch' : 'Preview'}
            </Button>
          </div>
        )}

        {/* Status indicator */}
        <div className="absolute top-3 left-3">
          <div className="w-8 h-8 bg-white/90 rounded-full flex items-center justify-center">
            {getStatusIcon()}
          </div>
        </div>

        {/* Duration badge */}
        {lecture.duration && (
          <div className="absolute bottom-3 right-3">
            <Badge variant="secondary" className="bg-black/60 text-white border-none">
              <Clock className="w-3 h-3 mr-1" />
              {formatDuration(lecture.duration)}
            </Badge>
          </div>
        )}

        {/* Preview badge */}
        {lecture.isPreviewFree && (
          <div className="absolute top-3 right-3">
            <Badge className="bg-blue-600 text-white">
              Preview
            </Badge>
          </div>
        )}
      </div>

      {/* Content Section */}
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Title and description */}
          <div>
            <h3 className="font-semibold text-gray-900 line-clamp-2 mb-1 group-hover:text-blue-600 transition-colors">
              {lecture.lectureTitle}
            </h3>
            {lecture.description && (
              <p className="text-sm text-gray-600 line-clamp-2">
                {lecture.description}
              </p>
            )}
          </div>

          {/* Content type indicators */}
          <div className="flex items-center gap-2">
            {lecture.videoUrl && (
              <Badge variant="outline" className="text-xs">
                <Video className="w-3 h-3 mr-1" />
                Video
              </Badge>
            )}
            {lecture.pdfUrl && (
              <Badge variant="outline" className="text-xs">
                <FileText className="w-3 h-3 mr-1" />
                PDF
              </Badge>
            )}
          </div>

          {/* Actions */}
          {showActions && (
            <div className="flex items-center justify-between pt-2">
              <div className="flex gap-2">
                <Button
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                  onClick={(e) => {
                    e.stopPropagation();
                    onPlay?.();
                  }}
                  disabled={isLocked}
                >
                  <Play className="w-3 h-3 mr-1" />
                  {variant === 'student' ? 'Watch' : 'Preview'}
                </Button>
                
                {variant === 'teacher' && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit?.();
                    }}
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    Edit
                  </Button>
                )}
              </div>

              {/* More actions dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={onPreview}>
                    <Eye className="w-4 h-4 mr-2" />
                    Preview
                  </DropdownMenuItem>
                  {onDownload && (
                    <DropdownMenuItem onClick={onDownload}>
                      <Download className="w-4 h-4 mr-2" />
                      Download
                    </DropdownMenuItem>
                  )}
                  {variant === 'teacher' && (
                    <>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem onClick={onEdit}>
                        <Edit className="w-4 h-4 mr-2" />
                        Edit Lecture
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={onDelete}
                        className="text-red-600 focus:text-red-600"
                      >
                        <FileText className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedLectureCard;
