import React from 'react';
import Video from 'next-video';
import { cn } from '@/lib/utils';

interface NextVideoPlayerProps {
  src: string;
  poster?: string;
  title?: string;
  onTimeUpdate?: (currentTime: number, duration: number) => void;
  onComplete?: () => void;
  onPlay?: () => void;
  onPause?: () => void;
  autoPlay?: boolean;
  controls?: boolean;
  muted?: boolean;
  loop?: boolean;
  className?: string;
}

const NextVideoPlayer: React.FC<NextVideoPlayerProps> = ({
  src,
  poster,
  title,
  onTimeUpdate,
  onComplete,
  onPlay,
  onPause,
  autoPlay = false,
  controls = true,
  muted = false,
  loop = false,
  className
}) => {
  const handleTimeUpdate = (event: React.SyntheticEvent<HTMLVideoElement>) => {
    const video = event.currentTarget;
    onTimeUpdate?.(video.currentTime, video.duration);
  };

  const handlePlay = () => {
    onPlay?.();
  };

  const handlePause = () => {
    onPause?.();
  };

  const handleEnded = () => {
    onComplete?.();
  };

  return (
    <div 
      className={cn(
        "relative bg-black rounded-lg overflow-hidden",
        "aspect-video w-full",
        className
      )}
    >
      {title && (
        <div className="absolute top-4 left-4 right-4 z-10">
          <h3 className="text-white font-medium text-lg drop-shadow-lg">
            {title}
          </h3>
        </div>
      )}
      
      <Video
        src={src}
        poster={poster}
        autoPlay={autoPlay}
        controls={controls}
        muted={muted}
        loop={loop}
        onTimeUpdate={handleTimeUpdate}
        onPlay={handlePlay}
        onPause={handlePause}
        onEnded={handleEnded}
        className="w-full h-full object-contain"
        style={{
          width: '100%',
          height: '100%',
        }}
        // Additional next-video specific props
        playsInline
        preload="metadata"
      />
    </div>
  );
};

export default NextVideoPlayer;
